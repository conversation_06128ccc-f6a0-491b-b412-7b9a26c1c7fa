/**
 * Assessment Tracking Utility
 * 
 * Provides comprehensive tracking and analytics for assessment progress,
 * user behavior, and performance metrics.
 */

// Event types for tracking
export const TRACKING_EVENTS = {
  // Assessment lifecycle
  ASSESSMENT_STARTED: 'assessment_started',
  ASSESSMENT_RESUMED: 'assessment_resumed',
  ASSESSMENT_COMPLETED: 'assessment_completed',
  ASSESSMENT_SUBMITTED: 'assessment_submitted',
  
  // Question interactions
  QUESTION_ANSWERED: 'question_answered',
  QUESTION_CHANGED: 'question_changed',
  QUESTION_SKIPPED: 'question_skipped',
  
  // Navigation
  PHASE_CHANGED: 'phase_changed',
  CATEGORY_CHANGED: 'category_changed',
  
  // User actions
  AUTO_FILL_USED: 'auto_fill_used',
  RANDOM_FILL_USED: 'random_fill_used',
  ASSESSMENT_RESET: 'assessment_reset',
  
  // Performance
  TIME_SPENT: 'time_spent',
  SESSION_DURATION: 'session_duration'
};

// Assessment types mapping
export const ASSESSMENT_TYPES = {
  VIA: 'via',
  RIASEC: 'riasec',
  BIG_FIVE: 'bigFive'
};

// Session storage for tracking data
const SESSION_KEY = 'assessmentSession';

/**
 * Assessment Session Manager
 * Tracks user session and behavior during assessment
 */
class AssessmentSession {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    this.events = [];
    this.currentPhase = null;
    this.currentCategory = null;
    this.questionStartTimes = new Map();
    
    this.loadSession();
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  loadSession() {
    try {
      const saved = sessionStorage.getItem(SESSION_KEY);
      if (saved) {
        const data = JSON.parse(saved);
        this.sessionId = data.sessionId || this.sessionId;
        this.startTime = data.startTime || this.startTime;
        this.events = data.events || [];
        this.currentPhase = data.currentPhase;
        this.currentCategory = data.currentCategory;
      }
    } catch (error) {
      console.warn('Failed to load session data:', error);
    }
  }

  saveSession() {
    try {
      const data = {
        sessionId: this.sessionId,
        startTime: this.startTime,
        events: this.events,
        currentPhase: this.currentPhase,
        currentCategory: this.currentCategory,
        lastUpdate: Date.now()
      };
      sessionStorage.setItem(SESSION_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save session data:', error);
    }
  }

  trackEvent(eventType, data = {}) {
    const event = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      type: eventType,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      phase: this.currentPhase,
      category: this.currentCategory,
      data: data
    };

    this.events.push(event);
    this.saveSession();

    // Log to console in development
    if (import.meta.env.DEV) {
      console.log('Assessment Event:', event);
    }

    return event;
  }

  setCurrentPhase(phase) {
    if (this.currentPhase !== phase) {
      this.trackEvent(TRACKING_EVENTS.PHASE_CHANGED, {
        fromPhase: this.currentPhase,
        toPhase: phase
      });
      this.currentPhase = phase;
      this.saveSession();
    }
  }

  setCurrentCategory(category) {
    if (this.currentCategory !== category) {
      this.trackEvent(TRACKING_EVENTS.CATEGORY_CHANGED, {
        fromCategory: this.currentCategory,
        toCategory: category
      });
      this.currentCategory = category;
      this.saveSession();
    }
  }

  startQuestionTimer(questionKey) {
    this.questionStartTimes.set(questionKey, Date.now());
  }

  endQuestionTimer(questionKey) {
    const startTime = this.questionStartTimes.get(questionKey);
    if (startTime) {
      const duration = Date.now() - startTime;
      this.questionStartTimes.delete(questionKey);
      return duration;
    }
    return null;
  }

  getSessionDuration() {
    return Date.now() - this.startTime;
  }

  getEventsByType(eventType) {
    return this.events.filter(event => event.type === eventType);
  }

  getPhaseStatistics(phase) {
    const phaseEvents = this.events.filter(event => event.phase === phase);
    const questionAnswered = phaseEvents.filter(event => event.type === TRACKING_EVENTS.QUESTION_ANSWERED);
    const questionChanged = phaseEvents.filter(event => event.type === TRACKING_EVENTS.QUESTION_CHANGED);
    
    return {
      totalEvents: phaseEvents.length,
      questionsAnswered: questionAnswered.length,
      questionsChanged: questionChanged.length,
      averageTimePerQuestion: this.calculateAverageQuestionTime(questionAnswered)
    };
  }

  calculateAverageQuestionTime(questionEvents) {
    if (questionEvents.length === 0) return 0;
    
    const totalTime = questionEvents.reduce((sum, event) => {
      return sum + (event.data.timeSpent || 0);
    }, 0);
    
    return totalTime / questionEvents.length;
  }

  clearSession() {
    try {
      sessionStorage.removeItem(SESSION_KEY);
      this.events = [];
      this.questionStartTimes.clear();
    } catch (error) {
      console.warn('Failed to clear session data:', error);
    }
  }

  exportSessionData() {
    return {
      sessionId: this.sessionId,
      startTime: this.startTime,
      duration: this.getSessionDuration(),
      events: this.events,
      statistics: {
        via: this.getPhaseStatistics(ASSESSMENT_TYPES.VIA),
        riasec: this.getPhaseStatistics(ASSESSMENT_TYPES.RIASEC),
        bigFive: this.getPhaseStatistics(ASSESSMENT_TYPES.BIG_FIVE)
      }
    };
  }
}

// Global session instance
let sessionInstance = null;

/**
 * Get or create assessment session instance
 */
export const getAssessmentSession = () => {
  if (!sessionInstance) {
    sessionInstance = new AssessmentSession();
  }
  return sessionInstance;
};

/**
 * Track assessment event
 */
export const trackAssessmentEvent = (eventType, data = {}) => {
  const session = getAssessmentSession();
  return session.trackEvent(eventType, data);
};

/**
 * Track question answer with timing
 */
export const trackQuestionAnswer = (questionKey, value, assessmentType, isChange = false) => {
  const session = getAssessmentSession();
  const timeSpent = session.endQuestionTimer(questionKey);
  
  const eventType = isChange ? TRACKING_EVENTS.QUESTION_CHANGED : TRACKING_EVENTS.QUESTION_ANSWERED;
  
  return session.trackEvent(eventType, {
    questionKey,
    value,
    assessmentType,
    timeSpent,
    isChange
  });
};

/**
 * Track phase navigation
 */
export const trackPhaseChange = (fromPhase, toPhase) => {
  const session = getAssessmentSession();
  session.setCurrentPhase(toPhase);
};

/**
 * Track category navigation
 */
export const trackCategoryChange = (fromCategory, toCategory) => {
  const session = getAssessmentSession();
  session.setCurrentCategory(toCategory);
};

/**
 * Start timing for a question
 */
export const startQuestionTimer = (questionKey) => {
  const session = getAssessmentSession();
  session.startQuestionTimer(questionKey);
};

/**
 * Get session statistics
 */
export const getSessionStatistics = () => {
  const session = getAssessmentSession();
  return session.exportSessionData();
};

/**
 * Clear all tracking data
 */
export const clearTrackingData = () => {
  if (sessionInstance) {
    sessionInstance.clearSession();
    sessionInstance = null;
  }
};

/**
 * Progress Analytics
 */
export const calculateCompletionRate = (answers, totalQuestions) => {
  const answeredCount = Object.keys(answers).length;
  return totalQuestions > 0 ? (answeredCount / totalQuestions) * 100 : 0;
};

export const estimateTimeRemaining = (currentProgress, sessionDuration) => {
  if (currentProgress <= 0) return null;
  
  const timePerQuestion = sessionDuration / currentProgress;
  const remainingQuestions = 100 - currentProgress; // Assuming percentage
  
  return timePerQuestion * remainingQuestions;
};

export const getProgressInsights = (assessmentType, answers, totalQuestions) => {
  const session = getAssessmentSession();
  const stats = session.getPhaseStatistics(assessmentType);
  const completionRate = calculateCompletionRate(answers, totalQuestions);
  const sessionDuration = session.getSessionDuration();
  
  return {
    completionRate,
    questionsAnswered: Object.keys(answers).length,
    totalQuestions,
    averageTimePerQuestion: stats.averageTimePerQuestion,
    estimatedTimeRemaining: estimateTimeRemaining(completionRate, sessionDuration),
    sessionDuration,
    changesCount: stats.questionsChanged
  };
};
