/**
 * Assessment Persistence Utility
 * 
 * Provides robust data persistence with multiple storage strategies,
 * data validation, migration support, and error recovery.
 */

// Storage configuration
const STORAGE_CONFIG = {
  PRIMARY_KEY: 'assessmentData_v2',
  BACKUP_KEY: 'assessmentData_backup',
  SESSION_KEY: 'assessmentSession_v2',
  VERSION: '2.0.0',
  MAX_RETRIES: 3,
  COMPRESSION_THRESHOLD: 50000 // bytes
};

// Data validation schemas
const VALIDATION_SCHEMAS = {
  answers: {
    via: (data) => typeof data === 'object' && data !== null,
    riasec: (data) => typeof data === 'object' && data !== null,
    bigFive: (data) => typeof data === 'object' && data !== null
  },
  scores: {
    via: (data) => data === null || (typeof data === 'object' && data !== null),
    riasec: (data) => data === null || (typeof data === 'object' && data !== null),
    bigFive: (data) => data === null || (typeof data === 'object' && data !== null)
  },
  currentStep: (data) => typeof data === 'number' && data >= 1 && data <= 3,
  timestamp: (data) => typeof data === 'number' && data > 0
};

/**
 * Storage Strategy Interface
 */
class StorageStrategy {
  constructor(name) {
    this.name = name;
  }

  isAvailable() {
    throw new Error('isAvailable method must be implemented');
  }

  save(key, data) {
    throw new Error('save method must be implemented');
  }

  load(key) {
    throw new Error('load method must be implemented');
  }

  remove(key) {
    throw new Error('remove method must be implemented');
  }

  clear() {
    throw new Error('clear method must be implemented');
  }
}

/**
 * LocalStorage Strategy
 */
class LocalStorageStrategy extends StorageStrategy {
  constructor() {
    super('localStorage');
  }

  isAvailable() {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  save(key, data) {
    try {
      const serialized = JSON.stringify(data);
      
      // Compress large data
      if (serialized.length > STORAGE_CONFIG.COMPRESSION_THRESHOLD) {
        // Simple compression placeholder - could implement actual compression
        console.warn('Large data detected, consider implementing compression');
      }
      
      localStorage.setItem(key, serialized);
      return true;
    } catch (error) {
      console.error('LocalStorage save failed:', error);
      return false;
    }
  }

  load(key) {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('LocalStorage load failed:', error);
      return null;
    }
  }

  remove(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('LocalStorage remove failed:', error);
      return false;
    }
  }

  clear() {
    try {
      // Only clear assessment-related keys
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('assessment')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
      return true;
    } catch (error) {
      console.error('LocalStorage clear failed:', error);
      return false;
    }
  }
}

/**
 * SessionStorage Strategy (fallback)
 */
class SessionStorageStrategy extends StorageStrategy {
  constructor() {
    super('sessionStorage');
  }

  isAvailable() {
    try {
      const test = '__storage_test__';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  save(key, data) {
    try {
      sessionStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('SessionStorage save failed:', error);
      return false;
    }
  }

  load(key) {
    try {
      const data = sessionStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('SessionStorage load failed:', error);
      return null;
    }
  }

  remove(key) {
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('SessionStorage remove failed:', error);
      return false;
    }
  }

  clear() {
    try {
      const keysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith('assessment')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => sessionStorage.removeItem(key));
      return true;
    } catch (error) {
      console.error('SessionStorage clear failed:', error);
      return false;
    }
  }
}

/**
 * Memory Strategy (last resort)
 */
class MemoryStorageStrategy extends StorageStrategy {
  constructor() {
    super('memory');
    this.storage = new Map();
  }

  isAvailable() {
    return true;
  }

  save(key, data) {
    try {
      this.storage.set(key, JSON.parse(JSON.stringify(data))); // Deep clone
      return true;
    } catch (error) {
      console.error('Memory storage save failed:', error);
      return false;
    }
  }

  load(key) {
    try {
      const data = this.storage.get(key);
      return data ? JSON.parse(JSON.stringify(data)) : null; // Deep clone
    } catch (error) {
      console.error('Memory storage load failed:', error);
      return null;
    }
  }

  remove(key) {
    try {
      this.storage.delete(key);
      return true;
    } catch (error) {
      console.error('Memory storage remove failed:', error);
      return false;
    }
  }

  clear() {
    try {
      const keysToRemove = [];
      for (const key of this.storage.keys()) {
        if (key.startsWith('assessment')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => this.storage.delete(key));
      return true;
    } catch (error) {
      console.error('Memory storage clear failed:', error);
      return false;
    }
  }
}

/**
 * Assessment Persistence Manager
 */
class AssessmentPersistenceManager {
  constructor() {
    this.strategies = [
      new LocalStorageStrategy(),
      new SessionStorageStrategy(),
      new MemoryStorageStrategy()
    ];
    
    this.primaryStrategy = this.findAvailableStrategy();
    this.backupStrategy = this.findBackupStrategy();
    
    console.log(`Using ${this.primaryStrategy.name} as primary storage`);
    if (this.backupStrategy) {
      console.log(`Using ${this.backupStrategy.name} as backup storage`);
    }
  }

  findAvailableStrategy() {
    return this.strategies.find(strategy => strategy.isAvailable()) || this.strategies[2]; // fallback to memory
  }

  findBackupStrategy() {
    const available = this.strategies.filter(strategy => 
      strategy.isAvailable() && strategy !== this.primaryStrategy
    );
    return available[0] || null;
  }

  validateData(data) {
    try {
      if (!data || typeof data !== 'object') return false;
      
      // Check version compatibility
      if (data.version && data.version !== STORAGE_CONFIG.VERSION) {
        console.warn('Data version mismatch, migration may be needed');
      }
      
      // Validate structure
      if (data.answers) {
        for (const [type, validator] of Object.entries(VALIDATION_SCHEMAS.answers)) {
          if (data.answers[type] && !validator(data.answers[type])) {
            console.warn(`Invalid answers data for ${type}`);
            return false;
          }
        }
      }
      
      if (data.scores) {
        for (const [type, validator] of Object.entries(VALIDATION_SCHEMAS.scores)) {
          if (data.scores[type] !== undefined && !validator(data.scores[type])) {
            console.warn(`Invalid scores data for ${type}`);
            return false;
          }
        }
      }
      
      if (data.currentStep && !VALIDATION_SCHEMAS.currentStep(data.currentStep)) {
        console.warn('Invalid currentStep data');
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Data validation failed:', error);
      return false;
    }
  }

  save(data) {
    const dataToSave = {
      ...data,
      version: STORAGE_CONFIG.VERSION,
      timestamp: Date.now()
    };

    let success = false;
    let retries = 0;

    while (!success && retries < STORAGE_CONFIG.MAX_RETRIES) {
      // Try primary strategy
      success = this.primaryStrategy.save(STORAGE_CONFIG.PRIMARY_KEY, dataToSave);
      
      if (!success) {
        retries++;
        console.warn(`Primary storage failed, retry ${retries}/${STORAGE_CONFIG.MAX_RETRIES}`);
      }
    }

    // Try backup strategy if primary fails
    if (!success && this.backupStrategy) {
      success = this.backupStrategy.save(STORAGE_CONFIG.BACKUP_KEY, dataToSave);
      if (success) {
        console.warn('Using backup storage for save operation');
      }
    }

    if (!success) {
      console.error('All storage strategies failed');
    }

    return success;
  }

  load() {
    // Try primary strategy first
    let data = this.primaryStrategy.load(STORAGE_CONFIG.PRIMARY_KEY);
    
    if (!data && this.backupStrategy) {
      // Try backup strategy
      data = this.backupStrategy.load(STORAGE_CONFIG.BACKUP_KEY);
      if (data) {
        console.warn('Loaded data from backup storage');
        // Restore to primary if possible
        this.primaryStrategy.save(STORAGE_CONFIG.PRIMARY_KEY, data);
      }
    }

    if (data && !this.validateData(data)) {
      console.warn('Loaded data failed validation, ignoring');
      return null;
    }

    return data;
  }

  remove() {
    let success = true;
    
    if (!this.primaryStrategy.remove(STORAGE_CONFIG.PRIMARY_KEY)) {
      success = false;
    }
    
    if (this.backupStrategy && !this.backupStrategy.remove(STORAGE_CONFIG.BACKUP_KEY)) {
      success = false;
    }
    
    return success;
  }

  clear() {
    let success = true;
    
    if (!this.primaryStrategy.clear()) {
      success = false;
    }
    
    if (this.backupStrategy && !this.backupStrategy.clear()) {
      success = false;
    }
    
    return success;
  }

  migrate(oldData) {
    // Implement data migration logic for version upgrades
    try {
      if (!oldData.version || oldData.version === '1.0.0') {
        // Migrate from v1 to v2
        const migratedData = {
          answers: oldData.answers || { via: {}, riasec: {}, bigFive: {} },
          scores: oldData.scores || { via: null, riasec: null, bigFive: null },
          currentStep: oldData.currentStep || 1,
          version: STORAGE_CONFIG.VERSION
        };
        
        console.log('Migrated data from v1.0.0 to v2.0.0');
        return migratedData;
      }
      
      return oldData;
    } catch (error) {
      console.error('Data migration failed:', error);
      return null;
    }
  }

  getStorageInfo() {
    return {
      primaryStrategy: this.primaryStrategy.name,
      backupStrategy: this.backupStrategy?.name || 'none',
      version: STORAGE_CONFIG.VERSION,
      isAvailable: this.primaryStrategy.isAvailable()
    };
  }
}

// Global instance
let persistenceManager = null;

/**
 * Get persistence manager instance
 */
export const getPersistenceManager = () => {
  if (!persistenceManager) {
    persistenceManager = new AssessmentPersistenceManager();
  }
  return persistenceManager;
};

/**
 * Save assessment data
 */
export const saveAssessmentData = (data) => {
  const manager = getPersistenceManager();
  return manager.save(data);
};

/**
 * Load assessment data
 */
export const loadAssessmentData = () => {
  const manager = getPersistenceManager();
  return manager.load();
};

/**
 * Clear assessment data
 */
export const clearAssessmentData = () => {
  const manager = getPersistenceManager();
  return manager.clear();
};

/**
 * Get storage information
 */
export const getStorageInfo = () => {
  const manager = getPersistenceManager();
  return manager.getStorageInfo();
};
